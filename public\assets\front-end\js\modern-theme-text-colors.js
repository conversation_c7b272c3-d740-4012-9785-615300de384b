/**
 * Modern Theme Text Color Changer
 * Changes specific text phrases to #9747FF color only on Modern theme home page
 */

(function() {
    'use strict';

    // Target color
    const TARGET_COLOR = '#9747FF';
    
    // List of exact text phrases to change
    const TARGET_PHRASES = [
        'Team Collaboration',
        'Workspaces and Statuses',
        'Increased',
        'Asked Questions',
        'Intuitive Project',
        'Contact Us',
        'Productivity',
        'Project & Task',
        'Unlimited Updates And Premium Support',
        'Feature',
        'project management'
    ];

    /**
     * Check if current page is Modern theme home page
     */
    function isModernThemeHomePage() {
        // Check if we're on the home page and not using classic theme
        const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.php' || window.location.pathname.includes('index');
        const isNotClassicTheme = !document.body.classList.contains('classic-theme') && !document.body.classList.contains('old-theme');
        return isHomePage && isNotClassicTheme;
    }

    /**
     * Wrap target text with colored span
     */
    function wrapTextWithColor(element, phrase) {
        const regex = new RegExp(`\\b${phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
        
        if (element.nodeType === Node.TEXT_NODE) {
            const text = element.textContent;
            if (regex.test(text)) {
                const newText = text.replace(regex, `<span style="color: ${TARGET_COLOR}">${phrase}</span>`);
                const wrapper = document.createElement('div');
                wrapper.innerHTML = newText;
                
                // Replace the text node with the new content
                const parent = element.parentNode;
                while (wrapper.firstChild) {
                    parent.insertBefore(wrapper.firstChild, element);
                }
                parent.removeChild(element);
            }
        }
    }

    /**
     * Process all text nodes in an element
     */
    function processTextNodes(element) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        // Process each phrase
        TARGET_PHRASES.forEach(phrase => {
            textNodes.forEach(textNode => {
                if (textNode.parentNode && textNode.textContent.includes(phrase)) {
                    wrapTextWithColor(textNode, phrase);
                }
            });
        });
    }

    /**
     * Alternative method using innerHTML replacement
     */
    function processElementsAlternative() {
        // Get all elements that might contain text
        const selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'span', 'div', 'a', 'li', 'td', 'th'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                let innerHTML = element.innerHTML;
                let hasChanges = false;

                TARGET_PHRASES.forEach(phrase => {
                    const regex = new RegExp(`\\b${phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
                    if (regex.test(innerHTML)) {
                        innerHTML = innerHTML.replace(regex, `<span style="color: ${TARGET_COLOR}">${phrase}</span>`);
                        hasChanges = true;
                    }
                });

                if (hasChanges) {
                    element.innerHTML = innerHTML;
                }
            });
        });
    }

    /**
     * Initialize the text color changes
     */
    function initTextColorChanges() {
        if (!isModernThemeHomePage()) {
            return;
        }

        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(processElementsAlternative, 100);
            });
        } else {
            setTimeout(processElementsAlternative, 100);
        }

        // Also run after a delay to catch any dynamically loaded content
        setTimeout(processElementsAlternative, 1000);
        setTimeout(processElementsAlternative, 3000);
    }

    // Initialize when script loads
    initTextColorChanges();

    // Export for manual triggering if needed
    window.modernThemeTextColors = {
        apply: processElementsAlternative,
        isActive: isModernThemeHomePage
    };

})();
