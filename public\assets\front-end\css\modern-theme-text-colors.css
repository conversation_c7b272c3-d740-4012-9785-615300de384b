/* Modern Theme Specific Text Color Changes */
/* Only applies to the Modern theme home page */

/* Target specific text phrases and change their color to #9747FF */

/* Use CSS to target text content containing specific phrases */
body:not(.classic-theme) {
    /* Team Collaboration */
    h1:contains("Team Collaboration"),
    h2:contains("Team Collaboration"),
    h3:contains("Team Collaboration"),
    h4:contains("Team Collaboration"),
    h5:contains("Team Collaboration"),
    h6:contains("Team Collaboration"),
    p:contains("Team Collaboration"),
    span:contains("Team Collaboration"),
    div:contains("Team Collaboration") {
        color: #9747FF !important;
    }

    /* Workspaces and Statuses */
    h1:contains("Workspaces and Statuses"),
    h2:contains("Workspaces and Statuses"),
    h3:contains("Workspaces and Statuses"),
    h4:contains("Workspaces and Statuses"),
    h5:contains("Workspaces and Statuses"),
    h6:contains("Workspaces and Statuses"),
    p:contains("Workspaces and Statuses"),
    span:contains("Workspaces and Statuses"),
    div:contains("Workspaces and Statuses") {
        color: #9747FF !important;
    }

    /* Increased */
    h1:contains("Increased"),
    h2:contains("Increased"),
    h3:contains("Increased"),
    h4:contains("Increased"),
    h5:contains("Increased"),
    h6:contains("Increased"),
    p:contains("Increased"),
    span:contains("Increased"),
    div:contains("Increased") {
        color: #9747FF !important;
    }

    /* Asked Questions */
    h1:contains("Asked Questions"),
    h2:contains("Asked Questions"),
    h3:contains("Asked Questions"),
    h4:contains("Asked Questions"),
    h5:contains("Asked Questions"),
    h6:contains("Asked Questions"),
    p:contains("Asked Questions"),
    span:contains("Asked Questions"),
    div:contains("Asked Questions") {
        color: #9747FF !important;
    }

    /* Intuitive Project */
    h1:contains("Intuitive Project"),
    h2:contains("Intuitive Project"),
    h3:contains("Intuitive Project"),
    h4:contains("Intuitive Project"),
    h5:contains("Intuitive Project"),
    h6:contains("Intuitive Project"),
    p:contains("Intuitive Project"),
    span:contains("Intuitive Project"),
    div:contains("Intuitive Project") {
        color: #9747FF !important;
    }

    /* Contact Us */
    h1:contains("Contact Us"),
    h2:contains("Contact Us"),
    h3:contains("Contact Us"),
    h4:contains("Contact Us"),
    h5:contains("Contact Us"),
    h6:contains("Contact Us"),
    p:contains("Contact Us"),
    span:contains("Contact Us"),
    div:contains("Contact Us"),
    a:contains("Contact Us") {
        color: #9747FF !important;
    }

    /* Productivity */
    h1:contains("Productivity"),
    h2:contains("Productivity"),
    h3:contains("Productivity"),
    h4:contains("Productivity"),
    h5:contains("Productivity"),
    h6:contains("Productivity"),
    p:contains("Productivity"),
    span:contains("Productivity"),
    div:contains("Productivity") {
        color: #9747FF !important;
    }

    /* Project & Task */
    h1:contains("Project & Task"),
    h2:contains("Project & Task"),
    h3:contains("Project & Task"),
    h4:contains("Project & Task"),
    h5:contains("Project & Task"),
    h6:contains("Project & Task"),
    p:contains("Project & Task"),
    span:contains("Project & Task"),
    div:contains("Project & Task") {
        color: #9747FF !important;
    }

    /* Unlimited Updates And Premium Support */
    h1:contains("Unlimited Updates And Premium Support"),
    h2:contains("Unlimited Updates And Premium Support"),
    h3:contains("Unlimited Updates And Premium Support"),
    h4:contains("Unlimited Updates And Premium Support"),
    h5:contains("Unlimited Updates And Premium Support"),
    h6:contains("Unlimited Updates And Premium Support"),
    p:contains("Unlimited Updates And Premium Support"),
    span:contains("Unlimited Updates And Premium Support"),
    div:contains("Unlimited Updates And Premium Support") {
        color: #9747FF !important;
    }

    /* Feature */
    h1:contains("Feature"),
    h2:contains("Feature"),
    h3:contains("Feature"),
    h4:contains("Feature"),
    h5:contains("Feature"),
    h6:contains("Feature"),
    p:contains("Feature"),
    span:contains("Feature"),
    div:contains("Feature") {
        color: #9747FF !important;
    }

    /* project management */
    h1:contains("project management"),
    h2:contains("project management"),
    h3:contains("project management"),
    h4:contains("project management"),
    h5:contains("project management"),
    h6:contains("project management"),
    p:contains("project management"),
    span:contains("project management"),
    div:contains("project management") {
        color: #9747FF !important;
    }
}
